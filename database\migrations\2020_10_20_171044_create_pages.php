<?php

use App\Enums\Status;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title', '191');
            $table->string('slug', '191')->unique();
            $table->longText('description');
            $table->unsignedBigInteger('footer_menu_section_id');
            $table->unsignedBigInteger('template_id');
            $table->unsignedTinyInteger('status')->default(Status::ACTIVE);
            $table->auditColumn();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pages');
    }
};
