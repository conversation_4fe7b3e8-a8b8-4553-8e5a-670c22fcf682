<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Click Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for Click payment gateway.
    | You can set your merchant ID, service ID, secret key and environment here.
    |
    */

    'merchant_id' => env('CLICK_MERCHANT_ID', ''),
    'service_id' => env('CLICK_SERVICE_ID', ''),
    'secret_key' => env('CLICK_SECRET_KEY', ''),
    'environment' => env('CLICK_ENVIRONMENT', 'sandbox'), // sandbox or production
    
    'sandbox_url' => 'https://testpay.click.uz',
    'production_url' => 'https://pay.click.uz',
    
    'currency' => 'UZS',
    'language' => 'ru', // ru, en, uz
    
    'timeout' => 30,
]; 