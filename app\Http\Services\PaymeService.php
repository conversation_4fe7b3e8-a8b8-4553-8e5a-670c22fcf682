<?php

namespace App\Http\Services;

use App\Http\Services\BasePaymentGateway;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymeService implements BasePaymentGateway
{
    protected $merchantId;
    protected $secretKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->merchantId = setting('payme_merchant_id');
        $this->secretKey = setting('payme_secret_key');
        $this->baseUrl = setting('payme_environment') === 'sandbox' 
            ? 'https://checkout.test.paycom.uz' 
            : 'https://checkout.paycom.uz';
    }

    public function configaration()
    {
        return [
            'merchant_id' => $this->merchantId,
            'secret_key' => $this->secretKey,
            'base_url' => $this->baseUrl
        ];   
    }

    public function payment(array $parameters)
    {
        try {
            $orderId = $parameters['order_id'] ?? uniqid('payme_');
            $amount = $parameters['amount'] ?? 0;
            $description = $parameters['description'] ?? 'Food delivery order';

            $data = [
                'merchant' => $this->merchantId,
                'amount' => $amount * 100, // Payme работает в тийинах (1 сум = 100 тийин)
                'currency' => 'UZS',
                'description' => $description,
                'order_id' => $orderId,
                'callback_url' => route('payme.callback'),
                'return_url' => route('payme.success'),
                'cancel_url' => route('payme.cancel'),
            ];

            // Создаем подпись для безопасности
            $signature = $this->generateSignature($data);
            $data['signature'] = $signature;

            // Отправляем запрос к Payme API
            $response = Http::post($this->baseUrl . '/api/payment', $data);

            if ($response->successful()) {
                $result = $response->json();
                if (isset($result['result']['checkout_url'])) {
                    return [
                        'success' => true,
                        'redirect_url' => $result['result']['checkout_url'],
                        'order_id' => $orderId
                    ];
                }
            }

            Log::error('Payme payment failed', [
                'response' => $response->body(),
                'data' => $data
            ]);

            return [
                'success' => false,
                'message' => 'Payment initialization failed'
            ];

        } catch (\Exception $e) {
            Log::error('Payme service error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Payment service error'
            ];
        }
    }

    public function redirectUrl()
    {
        // Этот метод может использоваться для получения URL для редиректа
        return null;
    }

    public function verifyPayment($paymentId)
    {
        try {
            $data = [
                'merchant' => $this->merchantId,
                'payment_id' => $paymentId
            ];

            $signature = $this->generateSignature($data);
            $data['signature'] = $signature;

            $response = Http::post($this->baseUrl . '/api/payment/verify', $data);

            if ($response->successful()) {
                $result = $response->json();
                return $result['result']['status'] === 'success';
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Payme verification error', [
                'message' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);
            return false;
        }
    }

    private function generateSignature($data)
    {
        // Создаем подпись для Payme API
        $signString = '';
        foreach ($data as $key => $value) {
            if ($key !== 'signature') {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString = rtrim($signString, '&');
        
        return hash_hmac('sha256', $signString, $this->secretKey);
    }
} 