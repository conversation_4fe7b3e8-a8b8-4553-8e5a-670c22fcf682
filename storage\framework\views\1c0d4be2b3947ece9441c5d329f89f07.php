<?php $__env->startPush('meta'); ?>
    <meta property="og:url" content="<?php echo e(route('checkout.index')); ?>">
    <meta property="og:type" content="Foodbank">
    <meta property="og:title" content="<?php echo e(setting('banner_title')); ?>">
    <meta property="og:description" content="Explore top-rated attractions, activities and more">
    <meta property="og:image" content="<?php echo e(asset('images/' . setting('site_logo'))); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('style'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('frontend/lib/inttelinput/css/intlTelInput.css')); ?>">
<?php $__env->stopPush(); ?>


<?php $__env->startSection('main-content'); ?>

    <!--=========  CHECKOUT PART Start =========-->
    <section class="checkout">
        <div class="container">

            <a href="<?php echo e(route('restaurant.show', $restaurant->slug)); ?>" class="booking-paginate">
                <i class="fa-solid fa-arrow-left"></i>
                <span><?php echo e(__('frontend.checkout')); ?></span>
            </a>

            <form id="payment-form" action="<?php echo e(route('checkout.store')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="checkout-group">
                    <div class="checkout-delivery">
                        <div class="checkout-card">
                            <?php if(!session()->get('cart')['delivery_type']): ?>

                                <div class="checkout-card-head">
                                    <h3><?php echo e(__('frontend.delivery_address')); ?></h3>
                                    <button type="button" data-bs-toggle="modal" id="add-new"
                                        data-bs-target="#address-modal" data-attr="<?php echo e(route('address.store')); ?>">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M8.00016 1.33301C4.32683 1.33301 1.3335 4.32634 1.3335 7.99967C1.3335 11.673 4.32683 14.6663 8.00016 14.6663C11.6735 14.6663 14.6668 11.673 14.6668 7.99967C14.6668 4.32634 11.6735 1.33301 8.00016 1.33301ZM10.6668 8.49967H8.50016V10.6663C8.50016 10.9397 8.2735 11.1663 8.00016 11.1663C7.72683 11.1663 7.50016 10.9397 7.50016 10.6663V8.49967H5.3335C5.06016 8.49967 4.8335 8.27301 4.8335 7.99967C4.8335 7.72634 5.06016 7.49967 5.3335 7.49967H7.50016V5.33301C7.50016 5.05967 7.72683 4.83301 8.00016 4.83301C8.2735 4.83301 8.50016 5.05967 8.50016 5.33301V7.49967H10.6668C10.9402 7.49967 11.1668 7.72634 11.1668 7.99967C11.1668 8.27301 10.9402 8.49967 10.6668 8.49967Z" />
                                        </svg>
                                        <span><?php echo e(__('levels.add_new_address')); ?> </span>
                                    </button>
                                </div>

                                <div class="text-danger">
                                    <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <?php echo e($message); ?>

                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <fieldset class="checkout-fieldset">
                                    <?php if(!blank($lastAddress)): ?>
                                        <label class="checkout-label" for="address">
                                            <input type="radio" value="<?php echo e($lastAddress->id); ?>" class="form-radio"
                                                onChange="deliveryAddress(<?php echo e($lastAddress->latitude); ?>,<?php echo e($lastAddress->longitude); ?>)"
                                                name="address" id="address">
                                            <dl>
                                                <dt> <?php echo e($lastAddress->label != \App\Enums\AddressType::OTHER
                                                    ? trans('address_types.' . $lastAddress->label)
                                                    : $lastAddress->label_name); ?>

                                                </dt>
                                                <dd>
                                                    <?php echo e($lastAddress->address); ?>

                                                </dd>
                                                <dd>
                                                    <?php echo e(__('levels.apartment')); ?> : <?php echo e($lastAddress->apartment); ?>

                                                </dd>
                                            </dl>
                                            <div>
                                                <a id="edit<?php echo e($lastAddress->id); ?>"
                                                    onclick="editBtn('<?php echo e($lastAddress->id); ?>')" data-bs-toggle="modal"
                                                    data-bs-target="#address-modal" class="action-btn mr-3"
                                                    data-url="<?php echo e(route('address.edit', $lastAddress->id)); ?>"
                                                    data-attr="<?php echo e(route('address.update', $lastAddress->id)); ?>">

                                                    <svg width="12" height="12" viewBox="0 0 12 12" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                            d="M4.25093 11.3519L10.1085 3.77696C10.4269 3.36847 10.5401 2.8962 10.4339 2.41533C10.342 1.97817 10.0731 1.56251 9.66991 1.24719L8.68657 0.466042C7.83057 -0.214774 6.76941 -0.143109 6.16102 0.638038L5.5031 1.49157C5.41821 1.59835 5.43943 1.75601 5.54554 1.84201C5.54554 1.84201 7.20802 3.17497 7.2434 3.20364C7.35659 3.31114 7.44148 3.45447 7.4627 3.62646C7.49807 3.96329 7.26462 4.27861 6.91797 4.32161C6.75526 4.34311 6.59963 4.29295 6.48644 4.19978L4.73906 2.80948C4.65417 2.7457 4.52683 2.75932 4.45609 2.84532L0.303426 8.22018C0.034599 8.55701 -0.057368 8.99416 0.034599 9.41698L0.565178 11.7174C0.593475 11.8393 0.699591 11.9253 0.82693 11.9253L3.16148 11.8966C3.58594 11.8894 3.98211 11.6959 4.25093 11.3519ZM7.51979 10.6355H11.3265C11.6979 10.6355 12 10.9415 12 11.3178C12 11.6947 11.6979 12 11.3265 12H7.51979C7.14839 12 6.84631 11.6947 6.84631 11.3178C6.84631 10.9415 7.14839 10.6355 7.51979 10.6355Z"
                                                            fill="#EE1D48" />
                                                    </svg>
                                                </a>

                                                <a id="delete<?php echo e($lastAddress->id); ?>"
                                                    onclick="deleteBtn(this,'<?php echo e($lastAddress->id); ?>')"
                                                    data-url="<?php echo e(route('address.delete', $lastAddress->id)); ?>"
                                                    class="action-btn">
                                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M14.0466 3.48634C12.9733 3.37967 11.8999 3.29967 10.8199 3.23967V3.23301L10.6733 2.36634C10.5733 1.75301 10.4266 0.833008 8.86661 0.833008H7.11994C5.56661 0.833008 5.41994 1.71301 5.31328 2.35967L5.17328 3.21301C4.55328 3.25301 3.93328 3.29301 3.31328 3.35301L1.95328 3.48634C1.67328 3.51301 1.47328 3.75967 1.49994 4.03301C1.52661 4.30634 1.76661 4.50634 2.04661 4.47967L3.40661 4.34634C6.89994 3.99967 10.4199 4.13301 13.9533 4.48634C13.9733 4.48634 13.9866 4.48634 14.0066 4.48634C14.2599 4.48634 14.4799 4.29301 14.5066 4.03301C14.5266 3.75967 14.3266 3.51301 14.0466 3.48634Z"
                                                            fill="#E93C3C" />
                                                        <path
                                                            d="M12.8202 5.42699C12.6602 5.26033 12.4402 5.16699 12.2135 5.16699H3.78683C3.56016 5.16699 3.33349 5.26033 3.18016 5.42699C3.02683 5.59366 2.94016 5.82033 2.95349 6.05366L3.36683 12.8937C3.44016 13.907 3.53349 15.1737 5.86016 15.1737H10.1402C12.4668 15.1737 12.5602 13.9137 12.6335 12.8937L13.0468 6.06033C13.0602 5.82033 12.9735 5.59366 12.8202 5.42699ZM9.10682 11.8337H6.88683C6.61349 11.8337 6.38683 11.607 6.38683 11.3337C6.38683 11.0603 6.61349 10.8337 6.88683 10.8337H9.10682C9.38016 10.8337 9.60682 11.0603 9.60682 11.3337C9.60682 11.607 9.38016 11.8337 9.10682 11.8337ZM9.66683 9.16699H6.33349C6.06016 9.16699 5.83349 8.94033 5.83349 8.66699C5.83349 8.39366 6.06016 8.16699 6.33349 8.16699H9.66683C9.94016 8.16699 10.1668 8.39366 10.1668 8.66699C10.1668 8.94033 9.94016 9.16699 9.66683 9.16699Z"
                                                            fill="#E93C3C" />
                                                    </svg>
                                                </a>
                                            </div>
                                        </label>
                                    <?php endif; ?>
                                    <button type="button" id="moreAddressShow" class="checkout-morebtn">
                                        <span><?php echo e(__('frontend.show_more')); ?> </span>
                                        <i class="fa-solid fa-chevron-down"></i>
                                    </button>
                                    <?php if(count($addresses) > 1): ?>
                                        <?php $__currentLoopData = $addresses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $address): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($address->id != (isset($lastAddress) ? $lastAddress->id : 0)): ?>
                                                <div class="moreAddress hide">
                                                    <label class="checkout-label" for="<?php echo e($address->id); ?>">
                                                        <input type="radio" class="form-radio"
                                                            onChange="deliveryAddress(<?php echo e($address->latitude); ?>,<?php echo e($address->longitude); ?>)"
                                                            value="<?php echo e($address->id); ?>" name="address"
                                                            id="<?php echo e($address->id); ?>">
                                                        <dl>
                                                            <dt><?php echo e($address->label != \App\Enums\AddressType::OTHER
                                                                ? trans('address_types.' . $address->label)
                                                                : $address->label_name); ?>

                                                            </dt>
                                                            <dd> <?php echo e($address->address); ?> </dd>
                                                            <dd> <?php echo e(__('levels.apartment')); ?> :
                                                                <?php echo e($address->apartment); ?> </dd>
                                                        </dl>
                                                        <div>
                                                            <a id="edit<?php echo e($address->id); ?>"
                                                                onclick="editBtn('<?php echo e($address->id); ?>')"
                                                                data-bs-toggle="modal" data-bs-target="#address-modal"
                                                                class="action-btn mr-3"
                                                                data-url="<?php echo e(route('address.edit', $address->id)); ?>"
                                                                data-attr="<?php echo e(route('address.update', $address->id)); ?>">

                                                                <svg width="12" height="12" viewBox="0 0 12 12"
                                                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                                        d="M4.25093 11.3519L10.1085 3.77696C10.4269 3.36847 10.5401 2.8962 10.4339 2.41533C10.342 1.97817 10.0731 1.56251 9.66991 1.24719L8.68657 0.466042C7.83057 -0.214774 6.76941 -0.143109 6.16102 0.638038L5.5031 1.49157C5.41821 1.59835 5.43943 1.75601 5.54554 1.84201C5.54554 1.84201 7.20802 3.17497 7.2434 3.20364C7.35659 3.31114 7.44148 3.45447 7.4627 3.62646C7.49807 3.96329 7.26462 4.27861 6.91797 4.32161C6.75526 4.34311 6.59963 4.29295 6.48644 4.19978L4.73906 2.80948C4.65417 2.7457 4.52683 2.75932 4.45609 2.84532L0.303426 8.22018C0.034599 8.55701 -0.057368 8.99416 0.034599 9.41698L0.565178 11.7174C0.593475 11.8393 0.699591 11.9253 0.82693 11.9253L3.16148 11.8966C3.58594 11.8894 3.98211 11.6959 4.25093 11.3519ZM7.51979 10.6355H11.3265C11.6979 10.6355 12 10.9415 12 11.3178C12 11.6947 11.6979 12 11.3265 12H7.51979C7.14839 12 6.84631 11.6947 6.84631 11.3178C6.84631 10.9415 7.14839 10.6355 7.51979 10.6355Z"
                                                                        fill="#EE1D48" />
                                                                </svg>
                                                            </a>

                                                            <a id="delete<?php echo e($address->id); ?>"
                                                                onclick="deleteBtn(this,'<?php echo e($address->id); ?>')"
                                                                data-url="<?php echo e(route('address.delete', $address->id)); ?>"
                                                                class="action-btn">
                                                                <svg width="16" height="16" viewBox="0 0 16 16"
                                                                    fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path
                                                                        d="M14.0466 3.48634C12.9733 3.37967 11.8999 3.29967 10.8199 3.23967V3.23301L10.6733 2.36634C10.5733 1.75301 10.4266 0.833008 8.86661 0.833008H7.11994C5.56661 0.833008 5.41994 1.71301 5.31328 2.35967L5.17328 3.21301C4.55328 3.25301 3.93328 3.29301 3.31328 3.35301L1.95328 3.48634C1.67328 3.51301 1.47328 3.75967 1.49994 4.03301C1.52661 4.30634 1.76661 4.50634 2.04661 4.47967L3.40661 4.34634C6.89994 3.99967 10.4199 4.13301 13.9533 4.48634C13.9733 4.48634 13.9866 4.48634 14.0066 4.48634C14.2599 4.48634 14.4799 4.29301 14.5066 4.03301C14.5266 3.75967 14.3266 3.51301 14.0466 3.48634Z"
                                                                        fill="#E93C3C" />
                                                                    <path
                                                                        d="M12.8202 5.42699C12.6602 5.26033 12.4402 5.16699 12.2135 5.16699H3.78683C3.56016 5.16699 3.33349 5.26033 3.18016 5.42699C3.02683 5.59366 2.94016 5.82033 2.95349 6.05366L3.36683 12.8937C3.44016 13.907 3.53349 15.1737 5.86016 15.1737H10.1402C12.4668 15.1737 12.5602 13.9137 12.6335 12.8937L13.0468 6.06033C13.0602 5.82033 12.9735 5.59366 12.8202 5.42699ZM9.10682 11.8337H6.88683C6.61349 11.8337 6.38683 11.607 6.38683 11.3337C6.38683 11.0603 6.61349 10.8337 6.88683 10.8337H9.10682C9.38016 10.8337 9.60682 11.0603 9.60682 11.3337C9.60682 11.607 9.38016 11.8337 9.10682 11.8337ZM9.66683 9.16699H6.33349C6.06016 9.16699 5.83349 8.94033 5.83349 8.66699C5.83349 8.39366 6.06016 8.16699 6.33349 8.16699H9.66683C9.94016 8.16699 10.1668 8.39366 10.1668 8.66699C10.1668 8.94033 9.94016 9.16699 9.66683 9.16699Z"
                                                                        fill="#E93C3C" />
                                                                </svg>
                                                            </a>
                                                        </div>
                                                    </label>
                                                </div>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </fieldset>
                            <?php else: ?>
                                <div class="checkout-card-head">
                                    <h3><?php echo e(__('frontend.pickup_location')); ?> </h3>
                                </div>

                                <label class="checkout-label d-block" for="address">
                                    <h6 class="mb-1"><?php echo e($restaurant->name); ?></h6>
                                    <dl>
                                        <dd> <?php echo e(__('frontend.address')); ?> : <?php echo e($restaurant->address); ?> </p>
                                        </dd>
                                    </dl>
                                </label>
                            <?php endif; ?>
                        </div>

                        <div class="checkout-card mb-0">
                            <div class="form-group">
                                <label class="form-label required"><?php echo e(__('frontend.phone_number')); ?></label>
                                <input class="form-control mobilenumber <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> phone"
                                    type="tel" id="number" name="mobile" onkeypress='validate(event)'>
                                <input type="hidden" id="code" name="countrycode" value="1">
                                <input type="hidden" id="code_name" name="countrycodename" value="us">

                                <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback d-block">
                                        <?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <input type="hidden" name="total_delivery_charge" id="total_delivery_charge"
                                value="0">

                            <div class="form-group mb-0">
                                <label class="form-label required"><?php echo e(__('frontend.payment_type')); ?></label>

                                <select class="form-select" name="payment_type" id="payment_type"
                                    onchange="myPaymentFunction()"
                                    class="form-control <?php $__errorArgs = ['payment_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> ">

                                    <option value="<?php echo e(App\Enums\PaymentMethod::CASH_ON_DELIVERY); ?>"
                                        <?php if(old('payment_type') == App\Enums\PaymentMethod::CASH_ON_DELIVERY): ?> selected="selected" <?php endif; ?>>
                                        <?php echo e(__('frontend.cash_on_delivery')); ?>

                                    </option>

                                    <?php if(auth()->user()->balance->balance >= $totalPayment): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::WALLET); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::WALLET): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.pay_with_credit_balance') . currencyFormatWithName(auth()->user()->balance->balance)); ?>

                                        </option>
                                    <?php endif; ?>

                                    <?php if(setting('stripe_key') && setting('stripe_secret')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::STRIPE); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::STRIPE): ?> selected="selected" <?php endif; ?>>

                                            <?php echo e(__('frontend.stripe')); ?>

                                        </option>
                                    <?php endif; ?>

                                    <?php if(setting('paystack_key') && setting('paystack_secret')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::PAYSTACK); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::PAYSTACK): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.paystack')); ?>

                                        </option>
                                    <?php endif; ?>

                                    <?php if(setting('paypal_client_id') && setting('paypal_client_secret')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::PAYPAL); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::PAYPAL): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.paypal')); ?>

                                        </option>
                                    <?php endif; ?>

                                    <?php if(setting('razorpay_key') && setting('razorpay_secret')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::RAZORPAY); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::RAZORPAY): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.razorpay')); ?>

                                        </option>
                                    <?php endif; ?>

                                    <?php if(setting('paytm_merchant_id') && setting('paytm_merchant_key')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::PAYTM); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::PAYTM): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.paytm')); ?>

                                        </option>
                                    <?php endif; ?>

                                    <?php if(setting('phonepe_merchant_id') && setting('phonepe_merchant_user_id') && setting('phonepe_salt_key')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::PHONEPE); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::PHONEPE): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.phonePe')); ?>

                                        </option>
                                    <?php endif; ?>


                                    <?php if(setting('sslcommerz_store_id') && setting('sslcommerz_store_password')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::SSLCOMMERZ); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::SSLCOMMERZ): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.sslcommerz')); ?>

                                        </option>
                                    <?php endif; ?>

                                    <?php if(setting('payme_merchant_id') && setting('payme_secret_key')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::PAYME); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::PAYME): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.payme')); ?>

                                        </option>
                                    <?php endif; ?>

                                    <?php if(setting('click_merchant_id') && setting('click_secret_key')): ?>
                                        <option value="<?php echo e(App\Enums\PaymentMethod::CLICK); ?>"
                                            <?php if(old('payment_type') == App\Enums\PaymentMethod::CLICK): ?> selected="selected" <?php endif; ?>>
                                            <?php echo e(__('frontend.click')); ?>

                                        </option>
                                    <?php endif; ?>

                                </select>
                                <?php $__errorArgs = ['payment_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback">
                                        <?php echo e($message); ?>

                                    </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                        </div>
                        <div class="row no-margin-row ms-2">
                            <div class="w-100 form-group col-sm-6 stripe-payment-method-div">
                                <label class="form-label"><?php echo e(__('frontend.credit_or_debit_card')); ?></label>
                                <div id="card-element"></div>
                                <div id="card-errors" class="text-danger" role="alert"></div>
                            </div>
                        </div>

                        <button type="submit" class="form-btn booking-confirmation-btn"
                            <?php if($menuitems['totalAmount'] <= 0): ?> disabled <?php endif; ?>>
                            <?php echo e(__('frontend.place_order')); ?>

                        </button>

                    </div>

                    <div class="checkout-summary">
                        <div class="checkout-summary-head">
                            <h3><?php echo e(__('frontend.order_summary')); ?> </h3>
                            <p>
                                <?php echo e(__('frontend.your_order_from') . ' ' . $restaurant->name); ?>

                            </p>
                        </div>
                        <ul class="checkout-summary-list">
                            <?php if(!blank($menuitems)): ?>
                                <?php $__currentLoopData = $menuitems['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="checkout-summary-item">
                                        <h3>
                                            <span><?php echo e($item['qty']); ?></span>
                                            <i class="fa-solid fa-xmark"></i>
                                        </h3>
                                        <dl>
                                            <dt><?php echo e($item['name']); ?> </dt>
                                            <?php if(isset($item['variation']['name']) && isset($item['variation']['price'])): ?>
                                                <dd class="fw-bold"><?php echo e($item['variation']['name']); ?> </dd>
                                            <?php endif; ?>
                                            <?php if(!blank($item['options'])): ?>
                                                <?php $__currentLoopData = $item['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <dd>+ <?php echo e($option['name']); ?></dd>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </dl>
                                        <h4><?php echo e(setting('currency_code')); ?><?php echo e($item['totalPrice']); ?> </h4>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php endif; ?>
                        </ul>
                        <ul class="checkout-summary-price-list">
                            <li>
                                <span><?php echo e(__('frontend.subtotal')); ?></span>
                                <span> <?php echo e(setting('currency_code')); ?><?php echo e($menuitems['subTotalAmount']); ?></span>
                            </li>

                            <?php if($menuitems['delivery_type'] != true): ?>
                                <li>
                                    <span><?php echo e(__('frontend.delivery_charge')); ?></span>
                                    <span><?php echo e(setting('currency_code')); ?><span id="delivery_chearge">0</span>
                                    </span>
                                </li>
                            <?php endif; ?>
                            <?php if(Schema::hasColumn('coupons', 'slug')): ?>
                                <li>
                                    <span><?php echo e(__('frontend.discount')); ?></span>
                                    <span> <?php echo e(setting('currency_code')); ?><?php echo e($menuitems['coupon_amount']); ?> </span>
                                </li>
                            <?php endif; ?>

                            <li>
                                <span><?php echo e(__('frontend.total')); ?></span>
                                <span><?php echo e(setting('currency_code')); ?><span id="total">0</span></span>
                            </li>
                        </ul>
                    </div>

                </div>
            </form>
        </div>
    </section>
    <!--======= CHECKOUT PART END =========-->


    <!--===== ADDRESS MODAL PART START =======-->
    <div class="modal fade address-modal" id="address-modal" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <form id="addressForm" method="post">
                    <input id="formMethod" type="hidden" name="_method" value="POST">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" name="lat" id="lat" value="">
                    <input type="hidden" name="long" id="long" value="">
                    <input type="hidden" name="id" id="id" value="">
                    <div class="address-modal-header">
                        <h3> <?php echo e(__('levels.add_new_address')); ?></h3>
                        <button class="fa-regular fa-circle-xmark" type="button" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="address-modal-search modalAddressSearch justify-content-between">
                        <i class="lni lni-search-alt"></i>

                        <div id="autocomplete-container" class="w-100">
                            <input id="autocomplete-input"
                                class="address autocomplete-input <?php $__errorArgs = ['new_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                name="new_address" type="text" placeholder="<?php echo e(__('frontend.search')); ?>">
                        </div>
                        <a href="javascript:void(0)">
                            <button id="locationIcon" onclick="getLocation()" class="lni lni-target iconSearch"></button>
                        </a>
                    </div>
                    <div class="">
                        <div id="googleMap" class="custom-map">

                        </div>
                    </div>
                    <div class="address-modal-details">
                        <label> <?php echo e(__('levels.apartment_flat')); ?></label>
                        <input id="apartment" type="text"
                            class="form-control <?php $__errorArgs = ['apartment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> apartment"
                            placeholder="<?php echo e(__('levels.apartment')); ?>" name="apartment" value="<?php echo e(old('apartment')); ?>">
                        <?php $__errorArgs = ['apartment'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback">
                                <?php echo e($message); ?>

                            </div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="address-modal-label-group">
                        <h4 class="address-modal-label-title"><?php echo e(__('levels.select_label')); ?></h4>

                        <select name="label" id="label" class="w-100 border-1">
                            <option value="" disabled selected><?php echo e(__('levels.select_label')); ?></option>

                            <?php $__currentLoopData = trans('address_types'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($key); ?>" <?= old('label') ? 'selected' : '' ?>>
                                    <?php echo e($value); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>

                        <?php $__errorArgs = ['label'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="text-danger check-errors1">
                                <?php echo e($message); ?>

                            </div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

                        <div id="other">
                            <input id="label_name" type="text"
                                class="address-modal-label-input label-name <?php $__errorArgs = ['label_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                placeholder="<?php echo e(__('levels.label_example')); ?>" name="label_name"
                                value="<?php echo e(old('label_name')); ?>">

                            <?php $__errorArgs = ['label_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger check-errors2">
                                    <?php echo e($message); ?>

                                </div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div class="text-danger jsalert">

                        </div>
                    </div>

                    <button class="form-btn" id="address-btn"><?php echo e(__('levels.confirm_location')); ?>

                    </button>
                </form>
            </div>
        </div>
    </div>
    <!--======== ADDRESS MODAL PART END ===========-->

<?php $__env->stopSection(); ?>


<?php $__env->startPush('js'); ?>
    <script defer src="<?php echo e(asset('frontend/lib/inttelinput/js/intlTelInput-jquery.js')); ?>"></script>
    <script defer src="<?php echo e(asset('frontend/lib/inttelinput/js/intlTelInput.js')); ?>"></script>
    <script defer src="<?php echo e(asset('frontend/lib/inttelinput/js/utils.js')); ?>"></script>
    <script defer src="<?php echo e(asset('frontend/lib/inttelinput/js/data.js')); ?>"></script>
    <script defer src="<?php echo e(asset('frontend/lib/inttelinput/js/init.js')); ?>"></script>
    <script src="<?php echo e(asset('js/phone_validation/index.js')); ?>"></script>
    <!-- For backend Js -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(setting('google_map_api_key')); ?>&libraries=places&callback=initMap">
    </script>
    <script src="<?php echo e(asset('frontend/js/checkout/map.js')); ?>"></script>
    <script>
        function deleteBtn(e, id) {
            let url = $(e).attr('data-url');
            var token = $("meta[name='csrf-token']").attr("content");
            if (confirm("Are you sure you want to delete this address ?")) {
                $.ajax({
                    url: url,
                    type: 'DELETE',
                    data: {
                        "id": id,
                        "_token": token,
                    },
                    success: function() {
                        iziToast.success({
                            title: 'Success',
                            message: 'Address Successfully Deleted.',
                            position: 'topRight'
                        });
                        window.location.reload();
                    }
                });
            }
        }

        let totalAmount = 0;

        function myPaymentFunction() {
            totalAmount = Number($('#total').text());
        }

        const siteName = "<?php echo e(setting('site_name')); ?>";
        let orderType = "<?php echo e(session()->get('cart')['delivery_type']); ?>";
        const siteLogo = "<?php echo e(asset('images/' . setting('site_logo'))); ?>";
        const currencyName = "<?php echo e(setting('currency_name')); ?>";
        const razorpayKey = "<?php echo e(env('RAZORPAY_KEY')); ?>";
        const stripeKey = "<?php echo e(setting('stripe_key')); ?>";
        const subtotal = "<?php echo e($menuitems['subTotalAmount']); ?>";
        const couponAmount = "<?php echo e($menuitems['coupon_amount']); ?>";
        const locationLat = parseFloat("<?php echo e($restaurant->lat); ?>");
        const locationLong = parseFloat("<?php echo e($restaurant->long); ?>");
        const freeZone = "<?php echo e(setting('free_delivery_radius')); ?>";
        const basicCharge = "<?php echo e(setting('basic_delivery_charge')); ?>";
        const chragePerKilo = "<?php echo e(setting('charge_per_kilo')); ?>";
        const delivery_type = "<?php echo e($menuitems['delivery_type']); ?>";

        const lastAddress = "<?php echo e(!blank($lastAddress) ? true : false); ?>";
        const lastAddress_latitude = parseFloat("<?php echo e(optional($lastAddress)->latitude ?? '23.8103'); ?>");
        const lastAddress_longitude = parseFloat("<?php echo e(optional($lastAddress)->longitude ?? '90.4125'); ?>");
    </script>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script src="https://js.stripe.com/v3/"></script>
    <script src="<?php echo e(asset('frontend/js/checkout/stripe.js')); ?>"></script>
    <script src="<?php echo e(asset('frontend/js/image-upload.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\OSPanel\domains\click\resources\views/frontend/restaurant/checkout.blade.php ENDPATH**/ ?>