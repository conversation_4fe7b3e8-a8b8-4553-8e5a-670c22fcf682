
<!-- JQUERY -->
<script src="https://code.jquery.com/jquery-3.7.1.js"></script>
<!-- DATERANGE -->
<script src="<?php echo e(asset('backend/lib/daterange/moment.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/lib/daterange/daterange.min.js')); ?>"></script>
<script defer src="<?php echo e(asset('backend/lib/daterange/daterange-init.js')); ?>"></script>
<!-- SWIPER -->
<script src="<?php echo e(asset('backend/lib/swiper/bundle.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/lib/swiper/initialize.js')); ?>"></script>
<!-- SCRIPTS -->
<script src="<?php echo e(asset('backend/js/dropdown.js')); ?>"></script>
<script src="<?php echo e(asset('backend/js/drawer.js')); ?>"></script>
<script src="<?php echo e(asset('backend/js/modal.js')); ?>"></script>
<script src="<?php echo e(asset('backend/js/tabs.js')); ?>"></script>
<script src="<?php echo e(asset('backend/js/script.js')); ?>"></script>
<!-- Template JS File -->
<script src="<?php echo e(asset('backend/lib/izitoast/dist/js/iziToast.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/js/scripts.js')); ?>"></script>
<script src="<?php echo e(asset('backend/js/confirm-delete.js')); ?>"></script>
<script src="<?php echo e(asset('js/custom.js')); ?>"></script>

<script src="<?php echo e(mix('js/app.js')); ?>" defer></script>
<script type="text/javascript">
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
    <?php if(session('success')): ?>
    iziToast.success({
        title: 'Success',
        message: '<?php echo e(session('success')); ?>',
        position: 'topRight'
    });
    <?php endif; ?>

    <?php if(session('error')): ?>
    iziToast.error({
        title: 'Error',
        message: '<?php echo e(session('error')); ?>',
        position: 'topRight'
    });
    <?php endif; ?>
</script>

<?php echo $__env->yieldPushContent('js'); ?>




<?php /**PATH D:\OSPanel\domains\click\resources\views/admin/layouts/script.blade.php ENDPATH**/ ?>