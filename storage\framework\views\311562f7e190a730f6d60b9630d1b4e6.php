<aside class="db-sidebar">
    <div class="db-sidebar-header">
        <a href="<?php echo e(route('home')); ?>" class="w-24"><img src="<?php echo e(themeSetting('site_logo') ? themeSetting('site_logo')->logo : asset('images/seeder/settings/logo.png')); ?>" alt="logo"></a>
        <button class="fa-solid fa-xmark xmark-btn"></button>
    </div>
    <nav class="db-sidebar-nav">
        <?php $__currentLoopData = $backendMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $menu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

            <?php if($menu['link'] === '#'): ?>
                <?php if(isset($menu['child'])): ?>
                    <h5 class="db-sidebar-nav-title"><?php echo e(trans('menu.' . $menu['name'])); ?></h5>
                    <ul class="db-sidebar-nav-list">
                        <?php $__currentLoopData = $menu['child']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="db-sidebar-nav-item <?php echo e(Request::is('admin/' . $child['link']) || Request::is('admin/' . $child['link'] . '/*') ? 'active' : ''); ?>">
                                <a href="<?php echo e(url('admin/' . $child['link'])); ?>" class="db-sidebar-nav-menu">
                                    <i class="<?php echo e($child['icon']); ?> text-sm"></i>
                                    <span class=""><?php echo e(trans('menu.' . $child['name'])); ?></span>
                                </a>
                            </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                <?php endif; ?>
            <?php else: ?>
                <ul class="db-sidebar-nav-list">
                    <li class="db-sidebar-nav-item <?php echo e(Request::is('admin/' . $menu['link'] . '*') ? 'active' : ''); ?>">
                        <a href="<?php echo e(url('admin/' . $menu['link'])); ?>" class="db-sidebar-nav-menu">
                            <i class="<?php echo e($menu['icon']); ?> text-sm"></i>
                            <span class=""><?php echo e(trans('menu.' . $menu['name'])); ?></span>
                        </a>
                    </li>
                </ul>
            <?php endif; ?>

        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </nav>
</aside>
<?php /**PATH D:\OSPanel\domains\click\resources\views/admin/layouts/sidebar.blade.php ENDPATH**/ ?>