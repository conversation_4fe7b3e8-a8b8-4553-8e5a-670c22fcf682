<?php

namespace App\Http\Controllers;


use Setting;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\LaravelInstaller\Helpers\EnvironmentManager;
use <PERSON>chid<PERSON><PERSON><PERSON><PERSON>\LaravelInstaller\Helpers\FinalInstallManager;
use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\LaravelInstaller\Helpers\InstalledFileManager;
use Rachid<PERSON><PERSON><PERSON>ri\LaravelInstaller\Events\LaravelInstallerFinished;

class FinalController extends \RachidLaasri\LaravelInstaller\Controllers\FinalController
{

    public function finish(InstalledFileManager $fileManager, FinalInstallManager $finalInstall, EnvironmentManager $environment)
    {
        $finalMessages = $finalInstall->runFinal();
        $finalStatusMessage = $fileManager->update();
        $finalEnvFile = $environment->getEnvContent();

        Setting::set([
            'license_code' => env('LICENSE_CODE')]);
        Setting::save();


        event(new LaravelInstallerFinished);

        return view('vendor.installer.finished', compact('finalMessages', 'finalStatusMessage', 'finalEnvFile'));
    }

}
