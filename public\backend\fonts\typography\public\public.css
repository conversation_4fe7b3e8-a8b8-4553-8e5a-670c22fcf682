@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 100;
    src: url(PublicSans-Thin.ttf) format('truetype');
}

@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 200;
    src: url(PublicSans-ExtraLight.ttf) format('truetype');
}

@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 300;
    src: url(PublicSans-Light.ttf) format('truetype');
}

@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 400;
    src: url(PublicSans-Regular.ttf) format('truetype');
}

@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 500;
    src: url(PublicSans-Medium.ttf) format('truetype');
}

@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 600;
    src: url(PublicSans-SemiBold.ttf) format('truetype');
}

@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 700;
    src: url(PublicSans-Bold.ttf) format('truetype');
}

@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 800;
    src: url(PublicSans-ExtraBold.ttf) format('truetype');
}

@font-face {
    font-family: 'Public Sans';
    font-style: normal;
    font-display: swap;
    font-weight: 900;
    src: url(PublicSans-Black.ttf) format('truetype');
}