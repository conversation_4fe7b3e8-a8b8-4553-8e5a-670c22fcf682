<div>

    <!--[if BLOCK]><![endif]--><?php if(!blank($carts)): ?>
        <!--~~~~~~ WHEN CART IS ORDER CODE START ~~~~~~~~-->
        <h2 class="cart-title"><?php echo e(__('frontend.mycart')); ?>

            (<span class="cartCount" id="carTNumber">
               <!--[if BLOCK]><![endif]--><?php if(!blank(session()->get('cart'))): ?>
                    <?php echo e(session()->get('cart')['totalQty']); ?>

                <?php else: ?>
                    0
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </span>)
        </h2>

        <div class="cart-scroll-group">
            <div class="d-flex justify-content-center aligjn-items-center mt-3 mb-3">
                <!--[if BLOCK]><![endif]--><?php if(
                    $restaurant->pickup_status == \App\Enums\Status::ACTIVE &&
                        $restaurant->delivery_status == \App\Enums\Status::ACTIVE): ?>
                  <div class="deliver_type my_toggle">
                    <div class="d_delivery<?php echo e($isActive ? '' : ' active'); ?>">
                        <?php echo e(__('frontend.delivery')); ?>

                    </div>
                    <div class="delivery_toglle">
                        <label class="switch" style="color: #f91942 !important">
                            <input type="checkbox" wire:model="isActive" wire:click="isUpdating"
                                <?php echo e(!blank($delivery_type) && $delivery_type == \App\Enums\DeliveryType::PICKUP ? 'checked' : ''); ?>>
                            <span class="slider slider-color btn-top round"></span>
                        </label>
                    </div>
                    <div class="d_pickup<?php echo e($isActive ? ' active' : ''); ?>">
                        <?php echo e(__('frontend.pickup')); ?>

                    </div>
                </div>

                <?php elseif(
                    $restaurant->delivery_status == \App\Enums\Status::INACTIVE &&
                        $restaurant->pickup_status == \App\Enums\Status::ACTIVE): ?>
                    <p class="delivery_status m-0 fw-semibold"><?php echo e(__('frontend.only_pickup')); ?></p>
                <?php elseif(
                    $restaurant->pickup_status == \App\Enums\Status::INACTIVE &&
                        $restaurant->delivery_status == \App\Enums\Status::ACTIVE): ?>
                    <p class="delivery_status m-0  fw-semibold"><?php echo e(__('frontend.only_delivery')); ?></p>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>

            <h3
                class="cart-heading <?php echo e($restaurant->delivery_status == \App\Enums\Status::INACTIVE || $restaurant->pickup_status == \App\Enums\Status::INACTIVE ? 'mt-0' : ''); ?> ">
                <?php echo e(__('frontend.your_order_from') . ' ' . \Illuminate\Support\Str::limit($restaurant->name, 26)); ?>

            </h3>

            <ul class="cart-list">

                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $carts['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li class="cart-item">
                        <button class="cart-delete"  wire:key="<?php echo e($key); ?>" wire:click.prevent="removeItem('<?php echo e($key); ?>')"
                            type="button">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M14.0466 3.48634C12.9733 3.37967 11.8999 3.29967 10.8199 3.23967V3.23301L10.6733 2.36634C10.5733 1.75301 10.4266 0.833008 8.86661 0.833008H7.11994C5.56661 0.833008 5.41994 1.71301 5.31328 2.35967L5.17328 3.21301C4.55328 3.25301 3.93328 3.29301 3.31328 3.35301L1.95328 3.48634C1.67328 3.51301 1.47328 3.75967 1.49994 4.03301C1.52661 4.30634 1.76661 4.50634 2.04661 4.47967L3.40661 4.34634C6.89994 3.99967 10.4199 4.13301 13.9533 4.48634C13.9733 4.48634 13.9866 4.48634 14.0066 4.48634C14.2599 4.48634 14.4799 4.29301 14.5066 4.03301C14.5266 3.75967 14.3266 3.51301 14.0466 3.48634Z"
                                    fill="#E93C3C" />
                                <path
                                    d="M12.8199 5.42699C12.6599 5.26033 12.4399 5.16699 12.2132 5.16699H3.78658C3.55991 5.16699 3.33325 5.26033 3.17991 5.42699C3.02658 5.59366 2.93991 5.82033 2.95325 6.05366L3.36658 12.8937C3.43991 13.907 3.53325 15.1737 5.85991 15.1737H10.1399C12.4666 15.1737 12.5599 13.9137 12.6332 12.8937L13.0466 6.06033C13.0599 5.82033 12.9732 5.59366 12.8199 5.42699ZM9.10658 11.8337H6.88658C6.61325 11.8337 6.38658 11.607 6.38658 11.3337C6.38658 11.0603 6.61325 10.8337 6.88658 10.8337H9.10658C9.37991 10.8337 9.60658 11.0603 9.60658 11.3337C9.60658 11.607 9.37991 11.8337 9.10658 11.8337ZM9.66658 9.16699H6.33325C6.05991 9.16699 5.83325 8.94033 5.83325 8.66699C5.83325 8.39366 6.05991 8.16699 6.33325 8.16699H9.66658C9.93991 8.16699 10.1666 8.39366 10.1666 8.66699C10.1666 8.94033 9.93991 9.16699 9.66658 9.16699Z"
                                    fill="#E93C3C" />
                            </svg>
                        </button>

                        <div class="cart-meta-group">
                            <h4 class="cart-name">
                                <?php echo e($content['name']); ?>

                            </h4>
                            <!--[if BLOCK]><![endif]--><?php if(isset($content['variation']['name']) && isset($content['variation']['price'])): ?>
                                <h5 class="cart-size"><?php echo e($content['variation']['name']); ?> </h5>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php if(!blank($content['options'])): ?>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $content['options']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <h6 class="cart-extra pt-2" wire:key="<?php echo e($option['id']); ?>">+ <?php echo e($option['name']); ?></h6>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                        <div class="cart-action-group">
                            <h5 class="cart-price"> <?php echo e(setting('currency_code')); ?><?php echo e($content['totalPrice']); ?> </h5>

                            <div class="cart-counter">
                                <button wire:click.prevent="removeItemQty('<?php echo e($key); ?>')"
                                    class="fa-solid fa-minus cart-counter-minus"></button>
                                <input type="number" step=".01" wire:model="carts.items.<?php echo e($key); ?>.qty"
                                    id="carts.items.<?php echo e($key); ?>.qty" class="cart-counter-value"
                                    wire:keyup="changeEvent('<?php echo e($key); ?>')" value="<?php echo e($content['qty']); ?>"
                                    min="1" max="99">
                                <button wire:click.prevent="addItemQty('<?php echo e($key); ?>')"
                                    class="fa-solid fa-plus cart-counter-plus"></button>
                            </div>
                        </div>
                    </li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

            </ul>
            <div class="cart-price-group">
                <!--[if BLOCK]><![endif]--><?php if(Schema::hasColumn('coupons', 'slug')): ?>
                    <div class="cart-coupon m-0">
                        <input wire:ignore wire:key="coupon" type="text" wire:model="coupon"
                            placeholder="<?php echo e(__('frontend.apply_coupon')); ?>">
                        <button wire:click.prevent="addCoupon()"><?php echo e(__('frontend.apply')); ?></button>
                    </div>
                    <span class="coupon-message fs-12 text-danger d-block mb-3 mt-2"><?php echo e($msg); ?></span>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <ul class="cart-amount-list">
                    <li class="cart-amount-item">
                        <span><?php echo e(__('frontend.subtotal')); ?></span>
                        <span><?php echo e(setting('currency_code')); ?><?php echo e($subTotalAmount); ?></span>
                    </li>

                    <!--[if BLOCK]><![endif]--><?php if($delivery_type == \App\Enums\DeliveryType::DELIVERY): ?>
                        <li class="cart-amount-item">
                            <span><?php echo e(__('frontend.delivery_charge')); ?></span>
                            <!--[if BLOCK]><![endif]--><?php if(setting('free_delivery') == 1 && $branch->postalCode()->max_order <= $subTotalAmount): ?>
                                <span> <?php echo e(__('levels.free')); ?></span>
                            <?php else: ?>
                                <span><?php echo e(setting('currency_code')); ?><?php echo e($delivery_charge); ?></span>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </li>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if(Schema::hasColumn('coupons', 'slug')): ?>
                        <li class="cart-amount-item">
                            <span><?php echo e(__('frontend.discount')); ?></span>
                            <span><?php echo e(setting('currency_code')); ?><?php echo e($discountAmount); ?> </span>
                        </li>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <li class="cart-amount-item">
                        <span><?php echo e(__('frontend.total')); ?></span>
                        <span><?php echo e(setting('currency_code')); ?><?php echo e($totalPayAmount); ?> </span>
                    </li>
                </ul>
            </div>
        </div>
        <div class="cart-amount-btn-div">
            <a href="<?php echo e(route('checkout.index')); ?>"
                class="cart-amount-btn <?php if(!blank($carts) && !blank($carts['items']) && $isActiveCheckout): ?> btn-checkout <?php else: ?> btn-checkout-disabled <?php endif; ?>"
                <?php if(!blank($carts) && !blank($carts['items']) && $isActiveCheckout): ?> onclick="return true;"
            <?php else: ?> onclick="return false;" <?php endif; ?>>
                <?php echo e(__('frontend.proceed_checkout')); ?>

            </a>



        </div>
        <!--~~~~~~  WHEN CART IS ORDER CODE END ~~~~~~~~~~~~-->
    <?php else: ?>
        <!--~~~~~  WHEN CART IS EMPTY CODE START ~~~~~~-->
        <div class="cart-empty">
            <h2 class="cart-title"><?php echo e(__('frontend.mycart')); ?>

                (<span class="cartCount" id="carTNumber">
                    <!--[if BLOCK]><![endif]--><?php if(!blank(session()->get('cart'))): ?>
                        <?php echo e(session()->get('cart')['totalQty']); ?>

                    <?php else: ?>
                        0
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </span>)
            </h2>
            <img src="<?php echo e(asset('frontend/images/gif/empty.gif')); ?>" alt="gif">
            <p> <?php echo e(__('frontend.cart_description')); ?> </p>
        </div>
        <!--~~~~ WHEN CART IS EMPTY CODE END ~~~~~~-->
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\OSPanel\domains\click\resources\views/livewire/order-cart.blade.php ENDPATH**/ ?>