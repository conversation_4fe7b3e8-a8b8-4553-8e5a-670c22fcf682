<?php $__env->startPush('meta'); ?>
    <meta property="og:url" content="<?php echo e(route('restaurant.show', [$restaurant->slug])); ?>" />
    <meta property="og:type" content="<?php echo e(setting('site_name')); ?>">
    <meta property="og:title" content="<?php echo e($restaurant->name); ?>">
    <meta property="og:description" content="<?php echo e($restaurant->description); ?>">
    <meta property="og:image" content="<?php echo e($restaurant->image); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startPush('body-data'); ?>
    data-bs-spy="scroll" data-bs-target="#scrollspy-menu" data-bs-smooth-scroll="true"
<?php $__env->stopPush(); ?>

<?php $__env->startSection('main-content'); ?>

    <!--====== RESTAURANT PART START =========-->
    <section class="restaurant">
        <div class="container">
            <div class="row">
                <div class="col-12 col-lg-8 rest-col">
                    <div class="rest-content">
                        <figure class="rest-media">
                            <a class="d-block" href="<?php echo e(route('restaurant.show', [$restaurant->slug])); ?>">
                                <img src="<?php echo e(asset($restaurant->image)); ?>" alt="restaurant">
                            </a>
                        </figure>

                        <div class="rest-profile">
                            <div class="rest-info">
                                <h1 class="rest-name">
                                    <?php if($restaurant->opening_time < $currenttime && $restaurant->closing_time > $currenttime): ?>
                                        <span class="dot on me-1" title="Open Now"></span>
                                    <?php else: ?>
                                        <span class="dot off me-1" title="Close Now"></span>
                                    <?php endif; ?>
                                    <?php echo e($restaurant->name); ?>

                                </h1>
                                <p class="rest-title">
                                    <?php if(!blank($restaurant->cuisines)): ?>
                                        <?php $__currentLoopData = $restaurant->cuisines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cuisine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php echo e($cuisine->name); ?>

                                            <?php if(!$loop->last): ?>
                                                <span>,</span>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </p>
                                <?php if(!$average_rating == 0): ?>
                                    <div class="rest-review">
                                        <?php for($i = 0; $i < 5; $i++): ?>
                                            <?php if($i < $average_rating): ?>
                                                <i class="fa-solid fa-star active"></i>
                                            <?php else: ?>
                                                <i class="fa-solid fa-star"></i>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                        <span>(<?php echo e($rating_user_count); ?> <?php echo e(__('frontend.reviews')); ?>)</span>
                                    </div>
                                <?php endif; ?>
                                <div class="rest-location">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M7.99992 8.95346C9.14867 8.95346 10.0799 8.02221 10.0799 6.87346C10.0799 5.7247 9.14867 4.79346 7.99992 4.79346C6.85117 4.79346 5.91992 5.7247 5.91992 6.87346C5.91992 8.02221 6.85117 8.95346 7.99992 8.95346Z"
                                            stroke="#1F1F39" stroke-width="1.5" />
                                        <path
                                            d="M2.4133 5.66016C3.72664 -0.113169 12.28 -0.106502 13.5866 5.66683C14.3533 9.0535 12.2466 11.9202 10.4 13.6935C9.05997 14.9868 6.93997 14.9868 5.5933 13.6935C3.7533 11.9202 1.64664 9.04683 2.4133 5.66016Z"
                                            stroke="#1F1F39" stroke-width="1.5" />
                                    </svg>
                                    <span> <?php echo e(\Illuminate\Support\Str::limit($restaurant->address, 65)); ?> </span>
                                </div>
                            </div>
                            <div class="rest-btns">
                                <?php if($restaurant->table_status == \App\Enums\TableStatus::ENABLE): ?>
                                    <button type="button" class="rest-book-btn" data-bs-toggle="modal"
                                        data-bs-target="#booking-modal">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path d="M5.3335 1.3335V3.3335" stroke="white" stroke-miterlimit="10"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M10.6665 1.3335V3.3335" stroke="white" stroke-miterlimit="10"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M2.3335 6.06006H13.6668" stroke="white" stroke-miterlimit="10"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path
                                                d="M14 5.66683V11.3335C14 13.3335 13 14.6668 10.6667 14.6668H5.33333C3 14.6668 2 13.3335 2 11.3335V5.66683C2 3.66683 3 2.3335 5.33333 2.3335H10.6667C13 2.3335 14 3.66683 14 5.66683Z"
                                                stroke="white" stroke-miterlimit="10" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                            <path d="M10.463 9.13314H10.469" stroke="white" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M10.463 11.1331H10.469" stroke="white" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M7.99715 9.13314H8.00314" stroke="white" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M7.99715 11.1331H8.00314" stroke="white" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M5.52938 9.13314H5.53537" stroke="white" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                            <path d="M5.52938 11.1331H5.53537" stroke="white" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <span><?php echo e(__('frontend.table')); ?> </span>
                                    </button>
                                <?php endif; ?>
                                <button type="button" class="rest-info-btn" data-bs-toggle="modal"
                                    data-bs-target="#shop-modal">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M8.00016 14.6668C11.6668 14.6668 14.6668 11.6668 14.6668 8.00016C14.6668 4.3335 11.6668 1.3335 8.00016 1.3335C4.3335 1.3335 1.3335 4.3335 1.3335 8.00016C1.3335 11.6668 4.3335 14.6668 8.00016 14.6668Z"
                                            stroke="#EE1D48" stroke-linecap="round" stroke-linejoin="round" />
                                        <path d="M8 5.3335V8.66683" stroke="#EE1D48" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                        <path d="M7.99609 10.6665H8.00208" stroke="#EE1D48" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <?php if(!empty($vouchers) && is_iterable($vouchers)): ?>
                            <?php $__currentLoopData = $vouchers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $voucher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="rest-voucher d-inline-block">
                                    <?php
                                        $amount = $voucher->discount_type == \App\Enums\DiscountType::FIXED
                                                ? currencyName(round($voucher->amount))
                                                : round($voucher->amount) . '%';
                                    ?>
                                    <button class="me-2">
                                        <?php echo e($voucher->restaurant_id == 0 ? __('frontend.coupon') : __('frontend.voucher')); ?>

                                        <span><?php echo e($voucher->slug); ?></span>
                                    </button>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                                
                        <div class="rest-menu-wrapper" id="scrollspy-menu">
                            <div class="rest-menu-group">
                                <button type="button" class="rest-swiper-prev fa-solid fa-chevron-left"></button>
                                <div class="swiper rest-swiper">
                                    <nav class="swiper-wrapper">
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <a href="#listing_product<?php echo e($category->id); ?>" wire:key="<?php echo e($category->id); ?>"
                                                class="swiper-slide">
                                                <?php echo e($category->name); ?>

                                            </a>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php if(!blank($other_products)): ?>
                                            <a href="#listing_product_other" class="swiper-slide">
                                                <?php echo e(__('frontend.other')); ?>

                                            </a>
                                        <?php endif; ?>
                                    </nav>
                                </div>
                                <button type="button" class="rest-swiper-next fa-solid fa-chevron-right"></button>
                            </div>
                        </div>

                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('show-page', ['restaurant' => $restaurant]);

$__html = app('livewire')->mount($__name, $__params, 'lw-3949214445-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                    </div>
                    <?php echo $__env->make('frontend.partials._footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                </div>

                <!--=======  Side bar card ========-->
                <aside class="cart-sidebar active">
                    <div class="cart-content">
                        <button class="cart-close fa-solid fa-xmark" type="button"></button>
                        <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('order-cart', ['restaurant' => $restaurant]);

$__html = app('livewire')->mount($__name, $__params, 'lw-3949214445-1', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                    </div>
                </aside>
            </div>
        </div>
    </section>
    <!--=======  RESTAURANT PART END ========-->

    <!--======= Add to Cart Modal Start  ========-->
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('show-cart', ['restaurant' => $restaurant]);

$__html = app('livewire')->mount($__name, $__params, 'lw-3949214445-2', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>


    <!--====== Table BOOKING MODAL START =========-->
    <div class="modal fade booking-modal" id="booking-modal" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="booking-modal-header">
                    <button class="fa-regular fa-circle-xmark" type="button" data-bs-dismiss="modal"></button>
                    <h3><?php echo e(__('frontend.table_booking')); ?> </h3>
                    <img src="<?php echo e(asset('frontend/images/gif/table.gif')); ?>" alt="gif">
                </div>
                <?php if($errors->any()): ?>
                    <div class="alert alert-danger">
                        <ul>
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <form action="<?php echo e(route('restaurant.reservation')); ?>" class="bookForm" method="GET">
                    <input type="hidden" name="restaurant_id" id="restaurant_id" value="<?php echo e($restaurant->id); ?>">
                    <div class="booking-modal-content">
                        <div class="booking-modal-group">
                            <div class="booking-modal-select">
                                <h4><span> <?php echo e(__('frontend.pick_date')); ?> </span>
                                    <span class="dateshow">
                                        <?php echo e(date('d M Y')); ?>

                                    </span>
                                </h4>
                                <i class="fa-solid fa-chevron-down"></i>
                            </div>
                            <div class="booking-modal-option">
                                <dl>
                                    <dt><?php echo e(__('frontend.choose_date')); ?> </dt>
                                    <dd class="date">
                                        <input type="date" name="reservation_date" value="<?php echo e(date('Y-m-d')); ?>" id="datePick">
                                    </dd>
                                </dl>
                                <button class="done" type="button"><?php echo e(__('frontend.done')); ?> </button>
                            </div>
                        </div>
                        <div class="booking-modal-group">
                            <div class="booking-modal-select">
                                <h4><span><?php echo e(__('frontend.number_guests')); ?> </span>
                                    <span class="guestQty guestotal d-inline">1</span>
                                    <span class="d-inline guestQty"><?php echo e(__('frontend.guests')); ?> </span>
                                </h4>
                                <i class="fa-solid fa-chevron-down"></i>
                            </div>
                            <div class="booking-modal-option">
                                <dl>
                                    <dt> <?php echo e(__('frontend.choose_guests_number')); ?> </dt>
                                    <dd class="cart-counter">
                                        <button type="button" class="fa-solid fa-minus qminus"></button>
                                        <input type="number" name="qtyInput" value="1" id="qtyInput"
                                            class="cart-counter-value">
                                        <button type="button" class="fa-solid fa-plus qplus"></button>
                                    </dd>
                                </dl>
                                <button class="done plusMinusBtn" type="button"><?php echo e(__('frontend.done')); ?></button>
                            </div>
                        </div>
                        <div class="booking-modal-time  <?php $__errorArgs = ['time_slot'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <h4><?php echo e(__('frontend.time_slots')); ?></h4>

                            <ul class="panel-dropdown-scrollable  reserveList" id="showTimeSlot">

                            </ul>

                        </div>

                        <div class="text-danger jsbook">

                        </div>
                    </div>
                    <div class="booking-modal-footer">
                        <button type="submit" id="bkkkid" class="cart-btn"><?php echo e(__('frontend.request_to_book')); ?> </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!--====== Table BOOKING MODAL PART END ==========-->


    <!--======= Resturent Infromation MODAL START =========-->
    <div class="modal fade shop-modal" id="shop-modal" data-bs-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="shop-modal-header">
                    <button class="fa-regular fa-circle-xmark" type="button" data-bs-dismiss="modal"></button>
                    <img src="<?php echo e($restaurant->image); ?>" alt="restaurant">
                </div>
                <div class="shop-modal-meta">
                    <h3><?php echo e($restaurant->name); ?> </h3>
                    <?php if(!blank($restaurant->cuisines)): ?>
                        <h4>
                            <?php $__currentLoopData = $restaurant->cuisines; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cuisine): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php echo e($cuisine->name); ?>

                                <?php if(!$loop->last): ?>
                                    <span>-</span>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </h4>
                    <?php endif; ?>
                    <p><?php echo e(__('frontend.open')); ?> <?php echo e(date('h:i A', strtotime($restaurant->opening_time))); ?> -
                        <?php echo e(date('h:i A', strtotime($restaurant->closing_time))); ?> </p>
                </div>
                <div class="nav nav-tabs">
                    <a class="nav-link active" data-bs-toggle="tab" href="#about"><?php echo e(__('frontend.about')); ?></a>
                    <a class="nav-link" data-bs-toggle="tab" href="#reviews"><?php echo e(__('frontend.reviews')); ?></a>
                </div>
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="about">
                        <div class="shop-modal-about">
                            <ul>
                                <li>
                                    <h3><?php echo e(__('frontend.delivery_hours')); ?> </h3>
                                    <p> <?php echo e(date('h:i A', strtotime($restaurant->opening_time))); ?> -
                                        <?php echo e(date('h:i A', strtotime($restaurant->closing_time))); ?> </p>
                                </li>
                                <li>
                                    <h3><?php echo e(__('frontend.address')); ?></h3>
                                    <p><?php echo e($restaurant->address); ?> </p>
                                </li>
                            </ul>
                            <img src="data:image/png;base64,<?php echo $qrCode; ?>" alt="qr">
                        </div>
                    </div>

                    <div class="tab-pane fade" id="reviews">

                        <?php if(!blank($order_status)): ?>
                            <form action="<?php echo e(route('restaurant.ratings-update')); ?>" method="POST"
                                enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>
                                <div id="add-review" class="add-review-box custom-width">
                                    <h5><?php echo e(__('frontend.add_review')); ?></h5>
                                    <hr>
                                    <div class="sub-ratings-container">
                                        <div class="add-sub-rating">
                                            <div class="sub-rating-title"><?php echo e(__('frontend.review')); ?>

                                                <i class="tip"
                                                    data-tip-content="<?php echo e(__('frontend.auality_customer')); ?>"></i>
                                            </div>
                                            <div class="sub-rating-stars">
                                                <div class="clearfix"></div>
                                                <div class="leave-rating">
                                                    <input class="d-none" type="radio" value="5" name="rating"
                                                        <?php echo e(5 == old('rating') ? 'checked' : ''); ?> id="rating-5">
                                                    <label for="rating-5" class="fa fa-star"></label>
                                                    <input class="d-none" type="radio" value="4" name="rating"
                                                        <?php echo e(4 == old('rating') ? 'checked' : ''); ?> id="rating-4">
                                                    <label for="rating-4" class="fa fa-star"></label>
                                                    <input class="d-none" type="radio" value="3" name="rating"
                                                        <?php echo e(3 == old('rating') ? 'checked' : ''); ?> id="rating-3">
                                                    <label for="rating-3" class="fa fa-star"></label>
                                                    <input class="d-none" type="radio" value="2" name="rating"
                                                        <?php echo e(2 == old('rating') ? 'checked' : ''); ?> id="rating-2">
                                                    <label for="rating-2" class="fa fa-star"></label>
                                                    <input class="d-none" type="radio" value="1" name="rating"
                                                        <?php echo e(1 == old('rating') ? 'checked' : ''); ?> id="rating-1">
                                                    <label for="rating-1" class="fa fa-star"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <input type="hidden" name="restaurant_id" value="<?php echo e($restaurant->id); ?>">
                                    <input type="hidden" name="status" value="5">

                                    <div class="form-group mt-2 pt-2">
                                        <label class="reviewLabel"><?php echo e(__('frontend.write_review')); ?> <span
                                                class="text-danger">*</span> </label>
                                        <textarea name="review" type="text" cols="40" rows="3" aria-label="With textarea"
                                            placeholder="<?php echo e(__('frontend.write_review')); ?> " class="form-control <?php $__errorArgs = ['review'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"><?php echo e(old('review')); ?></textarea>
                                        <?php if($errors->has('review')): ?>
                                            <span class="invalid-feedback" role="alert">
                                                <strong><?php echo e($errors->first('review')); ?></strong>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <button type="submit" class="rest-book-btn">
                                        <?php echo e(__('frontend.submit_review')); ?>

                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                        <br>
                        <?php if(!blank($ratings)): ?>
                            <ul class="shop-modal-review">
                                <?php $__currentLoopData = $ratings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rating): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>
                                        <dl>

                                            <?php for($i = 0; $i < 5; $i++): ?>
                                                <?php if($i < $rating->rating): ?>
                                                    <svg class="active" width="14" height="14"
                                                        viewBox="0 0 14 14" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M5.97191 1.37497C6.4383 0.599986 7.56186 0.599985 8.02825 1.37497L9.15178 3.24189C9.31933 3.5203 9.59263 3.71886 9.90919 3.79218L12.0319 4.28381C12.9131 4.48789 13.2603 5.55646 12.6674 6.23951L11.239 7.88495C11.026 8.13034 10.9216 8.45162 10.9497 8.77535L11.1381 10.9461C11.2163 11.8472 10.3073 12.5076 9.47449 12.1548L7.46819 11.3048C7.16899 11.1781 6.83117 11.1781 6.53197 11.3048L4.52568 12.1548C3.69283 12.5076 2.78386 11.8472 2.86206 10.9461L3.05045 8.77535C3.07855 8.45162 2.97416 8.13034 2.76115 7.88495L1.33279 6.23951C0.739863 5.55646 1.08706 4.48789 1.96824 4.28381L4.09097 3.79218C4.40753 3.71886 4.68083 3.5203 4.84838 3.24189L5.97191 1.37497Z"
                                                            stroke-width="1.5" />
                                                    </svg>
                                                <?php else: ?>
                                                    <svg width="14" height="14" viewBox="0 0 14 14"
                                                        fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M5.97191 1.37497C6.4383 0.599986 7.56186 0.599985 8.02825 1.37497L9.15178 3.24189C9.31933 3.5203 9.59263 3.71886 9.90919 3.79218L12.0319 4.28381C12.9131 4.48789 13.2603 5.55646 12.6674 6.23951L11.239 7.88495C11.026 8.13034 10.9216 8.45162 10.9497 8.77535L11.1381 10.9461C11.2163 11.8472 10.3073 12.5076 9.47449 12.1548L7.46819 11.3048C7.16899 11.1781 6.83117 11.1781 6.53197 11.3048L4.52568 12.1548C3.69283 12.5076 2.78386 11.8472 2.86206 10.9461L3.05045 8.77535C3.07855 8.45162 2.97416 8.13034 2.76115 7.88495L1.33279 6.23951C0.739863 5.55646 1.08706 4.48789 1.96824 4.28381L4.09097 3.79218C4.40753 3.71886 4.68083 3.5203 4.84838 3.24189L5.97191 1.37497Z"
                                                            stroke-width="1.5" />
                                                    </svg>
                                                <?php endif; ?>
                                            <?php endfor; ?>


                                            <div class="star-rating" data-rating="<?php echo e($rating->rating); ?>"> </div>

                                            <dd> <?php echo e($rating->updated_at->format('d M Y, h:i A')); ?></dd>
                                        </dl>
                                        <p><?php echo e($rating->review); ?> </p>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                            <br>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--======= Resturent Infromation MODAL END ==========-->
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script> const reservationUrl = "<?php echo e(route('reservation.check')); ?>";</script>
    <script src="<?php echo e(asset('frontend/js/booking.js')); ?>" type="text/javascript"></script>
    <script src="<?php echo e(asset('frontend/js/show.js')); ?>" type="text/javascript"></script>
    <script src="<?php echo e(asset('frontend/js/loader.js')); ?>" type="text/javascript"></script>
    <script src="<?php echo e(asset('frontend/js/navcount.js')); ?>" type="text/javascript"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('livewire'); ?>
    <script src="<?php echo e(asset('js/order-cart.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend.layouts.restaurent_app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\OSPanel\domains\click\resources\views/frontend/restaurant/show.blade.php ENDPATH**/ ?>