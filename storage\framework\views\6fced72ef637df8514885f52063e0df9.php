    <!--========= JS LINK PART START =====-->
    <script src="<?php echo e(asset('frontend/lib/jquery-3.5.0.min.js')); ?>"></script>
    <script src="<?php echo e(asset('frontend/lib/bootstrap/bootstrap.min.js')); ?>"></script>
    <script src="<?php echo e(asset('frontend/lib/swiper/swiper-bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset('frontend/lib/swiper/swiper-initialize.js')); ?>"></script>
    <script src="<?php echo e(asset('frontend/lib/izitoast/dist/js/iziToast.min.js')); ?>"></script>
    <script src="https://www.gstatic.com/firebasejs/8.3.2/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.3.2/firebase-messaging.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.3.2/firebase.js"></script>
    <!-- custom js Start -->
    <script type="application/javascript">
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    </script>

    <script type="text/javascript">
        <?php if(session('success')): ?>
            iziToast.success({
                title: 'Success',
                message: '<?php echo e(session('success')); ?>',
                position: 'topRight'
            });
        <?php endif; ?>

        <?php if(session('error')): ?>
            iziToast.error({
                title: 'Error',
                message: '<?php echo e(session('error')); ?>',
                position: 'topRight'
            });
        <?php endif; ?>

        <?php if(session('warning')): ?>
            iziToast.error({
                title: 'Warning',
                message: '<?php echo e(session('warning')); ?>',
                position: 'topRight'
            });
        <?php endif; ?>
    </script>

    <script type="text/javascript">
        $(document).ready(function() {
            const beep = document.getElementById("myAudio1");
            function sound() {
                beep.play();
            }
            const firebaseConfig = {
                apiKey: "<?php echo e(setting('firebase_api_key')); ?>",
                authDomain: "<?php echo e(setting('firebase_authDomain')); ?>",
                projectId: "<?php echo e(setting('projectId')); ?>",
                storageBucket: "<?php echo e(setting('storageBucket')); ?>",
                messagingSenderId: "<?php echo e(setting('messagingSenderId')); ?>",
                appId: "<?php echo e(setting('appId')); ?>",
                measurementId: "<?php echo e(setting('measurementId')); ?>",
            };
            firebase.initializeApp(firebaseConfig);
            const messaging = firebase.messaging();
            startFCM();
            function startFCM() {
                messaging.requestPermission()
                    .then(function() {
                        return messaging.getToken()
                    })
                    .then(function(response) {
                        $.ajax({
                            url: '<?php echo e(route('store.token')); ?>',
                            type: 'POST',
                            data: {
                                token: response
                            },
                            dataType: 'JSON',
                            success: function(response) {
                            },
                            error: function(error) {
                            },
                        });

                    }).catch(function(error) {});
            }

            messaging.onMessage(function(payload) {

                const title = payload.notification.title;
                const body = payload.notification.body;
                sound(); 
                new Notification(title, {
                    body: body,
                });
            });
        });
    </script>

    <?php echo $__env->yieldPushContent('js'); ?>
    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>

    <?php echo $__env->yieldPushContent('livewire'); ?>
    <script src="<?php echo e(asset('frontend/js/script.js')); ?>"></script>
<?php /**PATH D:\OSPanel\domains\click\resources\views/frontend/partials/_scripts.blade.php ENDPATH**/ ?>