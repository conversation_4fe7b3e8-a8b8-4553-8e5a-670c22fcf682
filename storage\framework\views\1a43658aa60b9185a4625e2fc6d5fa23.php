<header class="db-header">
    <a href="<?php echo e(route('home')); ?>" class="w-32 flex-shrink-0"><img class="w-full" src="<?php echo e(themeSetting('site_logo') ? themeSetting('site_logo')->logo : asset('images/seeder/settings/logo.png')); ?>" alt="logo"></a>
    <div class="flex items-center justify-end w-full gap-2">
        <div class="sub-header flex items-center gap-4 transition xh:justify-between xh:fixed xh:left-0 xh:w-full xh:p-4 xh:border-y xh:border-[#EFF0F6] xh:bg-white">
            <div class="flex items-center justify-between md:justify-center gap-4">

                <div class="language-group dropdown-group relative">
                    <?php if(isset($backendLanguage) && !empty($backendLanguage)): ?>
                        <?php $__currentLoopData = $backendLanguage; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if(Session()->has('applocale') and Session()->get('applocale') and setting('locale')): ?>
                                <?php if(Session()->get('applocale') == $lang->code): ?>
                                <button class="dropdown-btn flex items-center gap-2 h-9 px-3 rounded-lg bg-primary-light">
                                    <span> <?php echo e($lang->flag_icon); ?> </span>
                                    <span class="hidden md:block whitespace-nowrap text-xs font-medium capitalize text-heading"><?php echo e($lang->name); ?></span>
                                </button>
                                <?php endif; ?>
                            <?php elseif(setting('locale') == $lang->code): ?>
                                <button class="dropdown-btn flex items-center gap-2 h-9 px-3 rounded-lg bg-primary-light">
                                    <span> <?php echo e($lang->flag_icon); ?> </span>
                                    <span class="hidden md:block whitespace-nowrap text-xs font-medium capitalize text-heading"><?php echo e($lang->name); ?></span>
                                </button>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <ul class="p-2 min-w-[180px] rounded-lg shadow-xl absolute top-14 ltr:left-0 rtl:right-0 z-10 border border-gray-200 bg-white hidden dropdown-list">
                            <?php $__currentLoopData = $backendLanguage; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lang): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="flex items-center gap-2 rounded-md cursor-pointer hover:bg-gray-100">
                                <a href="<?php echo e(route('lang.index', $lang->code)); ?>" class="py-1.5 px-2.5">
                                    <span class="pr-2"><?php echo e($lang->flag_icon); ?></span>
                                    <span class="text-heading capitalize text-sm"><?php echo e($lang->name); ?></span>
                                </a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <button class="fa-solid fa-align-left db-header-nav w-9 h-9 rounded-lg text-primary bg-primary/5"></button>
        <?php if(auth()->guard()->check()): ?>
        <button data-account="#profileSidebar" class="flex items-center gap-1 sm:gap-2">
            <img class="flex-shrink w-9 h-9 object-cover rounded-lg" src="<?php echo e(auth()->user()->image); ?>" alt="avatar">
            <h3 class="whitespace-nowrap overflow-hidden text-ellipsis text-sm capitalize text-left leading-[17px]"><?php echo e(auth()->user()->getrole->name); ?> <b class="block whitespace-nowrap overflow-hidden text-ellipsis font-semibold"><?php echo e(__('Hi,')); ?> <?php echo e(auth()->user()->name); ?></b></h3>
            <i class="fa-solid fa-caret-down text-xs"></i>
        </button>
        <?php endif; ?>
    </div>
</header>
<?php /**PATH D:\OSPanel\domains\click\resources\views/admin/layouts/navigation.blade.php ENDPATH**/ ?>