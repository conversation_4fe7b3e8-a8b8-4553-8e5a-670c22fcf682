<?php $__env->startSection('main-content'); ?>
    <!--======== SETTINGS PART START =======-->
    <section class="settings">
        <div class="container">
            <div class="row">
                <?php echo $__env->make('frontend.account.partials._sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <div class="col-12 col-lg-8 col-xl-9">
                    <h3 class="order-details-title">
                        <a href="<?php echo e(route('account.order')); ?>">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M9.57 5.92969L3.5 11.9997L9.57 18.0697" stroke="#EE1D48" stroke-width="1.5"
                                    stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                                <path d="M20.4999 12H3.66992" stroke="#EE1D48" stroke-width="1.5" stroke-miterlimit="10"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </a>
                        <span><?php echo e(__('frontend.tracking')); ?> </span>
                    </h3>
                    <div class="pt-3">
                        <ul class="order-track">
                            <?php if($order->status == \App\Enums\OrderStatus::CANCEL): ?>
                                <li class="tracked active">
                                    <span class="line"></span>
                                    <i class="fa-solid fa-circle-check"></i>
                                    <span class="title"> <?php echo e(__('frontend.order_cancel')); ?></span>
                                </li>
                            <?php else: ?>
                                <li
                                    class=" <?php echo e($order->status >= \App\Enums\OrderStatus::PENDING ? 'tracked active' : ''); ?>">
                                    <span class="line"></span>
                                    <i class="fa-solid fa-circle-check"></i>
                                    <span class="title"><?php echo e(__('frontend.order_pending')); ?></span>
                                </li>
                            <?php endif; ?>

                            <?php if($order->status == \App\Enums\OrderStatus::REJECT): ?>
                                <li class="tracked active">
                                    <span class="line"></span>
                                    <i class="fa-solid fa-circle-check"></i>
                                    <span class="title"><?php echo e(__('frontend.order_reject')); ?></span>
                                </li>
                            <?php else: ?>
                                <li class="<?php echo e($order->status >= \App\Enums\OrderStatus::ACCEPT ? 'tracked active' : ''); ?>">
                                    <span class="line"></span>
                                    <i class="fa-solid fa-circle-check"></i>
                                    <span class="title"><?php echo e(__('frontend.order_accept')); ?></span>
                                </li>
                            <?php endif; ?>

                            <li class=" <?php echo e($order->status >= \App\Enums\OrderStatus::PROCESS ? 'tracked active' : ''); ?>">
                                <span class="line"></span>
                                <i class="fa-solid fa-circle-check"></i>
                                <span class="title"><?php echo e(__('frontend.order_process')); ?> </span>
                            </li>

                            <li
                                class=" <?php echo e($order->status >= \App\Enums\OrderStatus::ON_THE_WAY ? 'tracked active' : ''); ?>">
                                <span class="line"></span>
                                <i class="fa-solid fa-circle-check"></i>
                                <span class="title"><?php echo e(__('frontend.on_the_way')); ?> </span>
                            </li>
                            <li class="<?php echo e($order->status == \App\Enums\OrderStatus::COMPLETED ? 'tracked active' : ''); ?>">
                                <span class="line"></span>
                                <i class="fa-solid fa-circle-check"></i>
                                <span class="title"><?php echo e(__('frontend.order_completed')); ?> </span>
                            </li>
                        </ul>
                    </div>

                    <div class="order-details">
                        <ul class="order-meta ps-3 pe-3">
                            <li><span><?php echo e(__('frontend.order')); ?>:</span><a>#<?php echo e($order->order_code); ?></a></li>
                            <li><span> <?php echo e(__('frontend.order_date')); ?>

                                    :</span><span><?php echo e($order->created_at->format('d M Y, h:i A')); ?></span></li>
                        </ul>
                        <div class="order-group">
                            <div class="order-box address">
                                <h4 class="order-box-title"><?php echo e(__('frontend.billing_to')); ?> </h4>
                                <ul class="order-box-list">
                                    <li class="order-box-item"><b><?php echo e(__('frontend.name')); ?> :</b>
                                        <p><?php echo e($order->user->name ?? ''); ?> </p>
                                    </li>
                                    <li class="order-box-item"><b><?php echo e(__('frontend.phone')); ?> :</b>
                                        <p><?php echo e($order->mobile ?? ''); ?></p>
                                    </li>
                                    <li class="order-box-item"><b><?php echo e(__('frontend.address')); ?> :</b>
                                        <p> <?php echo e(orderAddress($order->address)); ?> </p>
                                    </li>
                                </ul>
                            </div>
                            <div class="order-box status">
                                <h4 class="order-box-title"><?php echo e(__('frontend.delivery_status')); ?> </h4>
                                <ul class="order-box-list">
                                    <li class="order-box-item"><b> <?php echo e(__('frontend.order_status')); ?> :</b><span
                                            class="badge-text blue"> <?php echo e(trans('order_status.' . $order->status)); ?></span>
                                    </li>
                                    <li class="order-box-item"><b><?php echo e(__('levels.order_type')); ?> :</b>
                                        <p> <?php echo e($order->getOrderType); ?></p>
                                    </li>
                                    <li class="order-box-item"><b><?php echo e(__('frontend.payment_status')); ?> :</b>
                                        <?php if($order->payment_status == \App\Enums\PaymentStatus::PAID): ?>
                                            <span class="badge-text green">
                                                <?php echo e(trans('payment_status.' . $order->payment_status) ?? null); ?></span>
                                        <?php else: ?>
                                            <span class="badge-text red">
                                                <?php echo e(trans('payment_status.' . $order->payment_status) ?? null); ?></span>
                                        <?php endif; ?>
                                    </li>
                                    <li class="order-box-item"><b><?php echo e(__('frontend.payment_method')); ?> :</b>
                                        <p> <?php echo e(trans('payment_method.' . $order->payment_method) ?? null); ?> </p>
                                    </li>

                                </ul>
                            </div>
                        </div>

                        <?php if(session('status')): ?>
                            <div class="alert alert-success" role="alert">
                                <?php echo e(session('status')); ?>

                            </div>
                        <?php endif; ?>
                        <table class="table table-order">
                            <thead class="table-primary">
                                <tr>
                                    <th scope="col"><?php echo e(__('frontend.item')); ?></th>
                                    <th scope="col"><?php echo e(__('frontend.price')); ?></th>
                                    <th scope="col"><?php echo e(__('frontend.quantity')); ?></th>
                                    <th scope="col"><?php echo e(__('frontend.totals')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itemKey => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td data-title="item">
                                            <dl class="order-table-item">
                                                <dt>
                                                    <?php echo e($item->menuItem->name); ?>

                                                    <?php echo e($item->variation ? ' ( ' . $item->variation['name'] . ' )' : ''); ?>

                                                </dt>
                                                <?php if($item->options): ?>
                                                    <dd>
                                                        <?php $__currentLoopData = json_decode($item->options, true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <span><?php echo e($option['name']); ?></span>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </dd>
                                                <?php endif; ?>
                                            </dl>
                                        </td>
                                        <td data-title="price">
                                            <div class="order-table-price">
                                                <span> <?php echo e(currencyFormat($item->unit_price)); ?></span>
                                            </div>
                                        </td>
                                        <td data-title="qnty"><?php echo e($item->quantity); ?> </td>
                                        <td data-title="total"><?php echo e(currencyFormat($item->item_total)); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>

                        <ul class="order-price-list">
                            <?php if($order->discount && $order->discount->amount > 0 && Schema::hasColumn('coupons', 'slug')): ?>
                                <li>
                                    <span><?php echo e(__('frontend.discount')); ?></span>
                                    <span class="pe-xxl-4 pe-xl-3 pe-lg-1 pe-md-1">
                                        <?php echo e(currencyFormat($order->discount->amount)); ?>

                                    </span>
                                </li>
                            <?php endif; ?>

                            <li><span><?php echo e(__('frontend.subtotal')); ?> </span>
                                <span class="pe-xxl-4 pe-xl-3 pe-lg-1 pe-md-1">
                                    <?php echo e(currencyFormat($order->sub_total)); ?> </span>
                            </li>

                            <li><span><?php echo e(__('frontend.delivery_charge')); ?></span>
                                <span
                                    class="pe-xxl-4 pe-xl-3 pe-lg-1 pe-md-1"><?php echo e(currencyFormat($order->delivery_charge)); ?></span>
                            </li>
                            <li>
                                <span><?php echo e(__('frontend.total')); ?></span>
                                <span class="pe-xxl-4 pe-xl-3 pe-lg-1 pe-md-1"><?php echo e(currencyFormat($order->total)); ?></span>
                            </li>
                        </ul>
                    </div>

                    <div id="invoice-print" class="d-none">
                        <div class="invoice">
                            <style>
                                .invoice {
                                    max-width: 390px;
                                    width: 100%;
                                    margin: auto;
                                    padding: 8px;
                                    font-family: 'OpenSauceOne', sans-serif;
                                }

                                p {
                                    margin-top: 4px;
                                    margin-bottom: 0px;
                                }

                                h2,
                                h3 {
                                    font-size: 32px;
                                    font-weight: bolder;
                                    margin-top: 4px;
                                    margin-bottom: 2px;
                                }

                                h3 {
                                    font-size: 28px;
                                    margin-bottom: 8px;
                                }

                                p,
                                td {
                                    font-size: 16px;
                                }

                                table {
                                    width: 100%;
                                    margin-top: 4px;
                                    margin-bottom: 4px;
                                }

                                .invoiceFooter p {
                                    font-size: 14px;
                                    font-weight: 400;

                                }

                                .invoiceFooter small {
                                    font-size: 12px;
                                    margin-top: 24px;
                                }

                                .border-dashed {
                                    border-top: 1px dashed gainsboro;
                                }

                                .text-center {
                                    text-align: center;
                                }

                                .text-start {
                                    text-align: start;
                                }

                                .text-end {
                                    text-align: end;
                                }

                                .align-top {
                                    vertical-align: top;
                                }

                                .min-w-80 {
                                    min-width: 80px;
                                    width: 80px;
                                }

                                ul {
                                    list-style: none;
                                }
                            </style>
                            <div class="text-center pb-2">
                                <h2> <?php echo e(setting('site_name') ? setting('site_name') : ''); ?>

                                    <?php echo e(__('frontend.restaurant')); ?></h2>
                                <h3><?php echo e(__('frontend.food_ordering_delivery_system')); ?></h3>
                                <p> <?php echo e(__('frontend.email')); ?>: <?php echo e(setting('site_email')); ?></p>
                                <p class="mt-2"> <?php echo e(__('frontend.tel')); ?>:
                                    <?php echo e(setting('site_phone_number')); ?>

                                </p>
                            </div>
                            <div class="border-dashed">
                                <ul>
                                    <li class="pt-1">
                                        <td class="text-start">#<?php echo e($order->order_code); ?></td>
                                    </li>
                                    <li class="pb-1 d-flex justify-content-between align-items-center">
                                        <span class="text-start mt-1"> <?php echo e($order->created_at->format('d M Y')); ?></span>
                                        <span class="text-end"><?php echo e($order->created_at->format('h:i A')); ?></span>
                                    </li>
                                </ul>
                            </div>
                            <div class="border-dashed">
                                <ul class="d-flex align-items-center justify-content-between">
                                    <li class="text-start pb-3">
                                        <?php echo e(__('frontend.quantity')); ?></li>
                                    </li>
                                    <li class="text-start pb-3">
                                        <?php echo e(__('frontend.item')); ?> </li>
                                    <li class="text-end pb-3">
                                        <?php echo e(__('frontend.totals')); ?></li>
                                </ul>

                                <?php $__currentLoopData = $items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $itemKey => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <ul class="d-flex align-items-start justify-content-between">
                                        <li class="text-start align-top pb-2 min-w-80"> <?php echo e($item->quantity); ?></li>
                                        <li class="text-start pb-2"> <?php echo e($item->menuItem->name); ?>

                                            <?php echo e($item->variation ? ' ( ' . $item->variation['name'] . ' )' : ''); ?>

                                            <?php if($item->options): ?>
                                                <p>
                                                    <?php $__currentLoopData = json_decode($item->options, true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <span><?php echo e($option['name']); ?></span>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </p>
                                            <?php endif; ?>
                                        </li>
                                        <li class="text-end align-top pb-2 min-w-80">
                                            <?php echo e(currencyFormat($item->item_total)); ?>

                                        </li>
                                    </ul>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </div>
                            <div class="border-dashed ps-5">
                                <ul class="ms-4">
                                    <li class="d-flex align-items-center justify-content-between">
                                        <span class="text-start"> <?php echo e(__('frontend.subtotal')); ?>:</span>
                                        <span class="text-end align-top"><?php echo e(currencyFormat($order->sub_total)); ?></span>
                                    </li>
                                    <?php if($order->discount && $order->discount->amount > 0 && Schema::hasColumn('coupons', 'slug')): ?>
                                        <li class="d-flex align-items-center justify-content-between">
                                            <span class="text-start"> <?php echo e(__('frontend.discount')); ?>:
                                            </span>
                                            <span class="text-end align-top">
                                                <?php echo e(currencyFormat($order->discount->amount)); ?></span>
                                        </li>
                                    <?php endif; ?>
                                    <li class="d-flex align-items-center justify-content-between">
                                        <span class="text-start"> <?php echo e(__('frontend.delivery_charge')); ?>:
                                        </span>
                                        <span class="text-end align-top"><?php echo e(currencyFormat($order->delivery_charge)); ?>

                                        </span>
                                    </li>
                                    <li class="d-flex align-items-center justify-content-between">
                                        <span class="text-start"> <?php echo e(__('frontend.total')); ?>:</span>
                                        <span class="text-end align-top"> <?php echo e(currencyFormat($order->total)); ?></span>
                                    </li>
                                </ul>
                            </div>
                            <div class="border-dashed">
                                <ul class="pt-1 pb-1">
                                    <li class="d-flex align-items-center">
                                        <span><?php echo e(__('levels.order_type')); ?>:</span>
                                        <span><?php echo e($order->getOrderType); ?></span>
                                    </li>
                                    <li class="d-flex align-items-center">
                                        <span><?php echo e(__('frontend.payment_status')); ?>:</span>
                                        <span><?php echo e(trans('payment_status.' . $order->payment_status) ?? null); ?></span>
                                    </li>
                                </ul>
                            </div>
                            <div class="border-dashed">
                                <ul class="pt-1 pb-1">
                                    <li class="d-flex">
                                        <span class="pb-2 min-w-80"><?php echo e(__('levels.customer')); ?>:</span>
                                        <span><?php echo e($order->user->name ?? ''); ?></span>
                                    </li>
                                    <li class="d-flex">
                                        <span class="pb-2 min-w-80"><?php echo e(__('frontend.phone')); ?>: </span>
                                        <span><?php echo e($order->mobile ?? ''); ?></span>
                                    </li>
                                    <li class="d-flex">
                                        <span class="pb-2 min-w-80"><?php echo e(__('frontend.address')); ?>:</span>
                                        <span><?php echo e(orderAddress($order->address)); ?></span>
                                    </li>
                                </ul>
                            </div>
                            <p class="text-center border-dashed pt-2"> <?php echo e(__('levels.thank_you')); ?> </p>
                            <div class="text-end invoiceFooter mt-4">
                                <small><?php echo e(setting('site_name') ? setting('site_name') : ''); ?></small>
                                <p><?php echo e(__('frontend.restaurant')); ?> <?php echo e(__('frontend.food_ordering_delivery_system')); ?>

                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="order-btns pt-4">
                        <?php if($order->status == \App\Enums\OrderStatus::PENDING): ?>
                            <a href="<?php echo e(route('account.order.cancel', $order)); ?>"
                                onclick="return confirm('<?php echo e(__('frontend.cancel_message')); ?>')">
                                <button class="cancel" type="button">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M10 0C4.49 0 0 4.49 0 10C0 15.51 4.49 20 10 20C15.51 20 20 15.51 20 10C20 4.49 15.51 0 10 0ZM13.36 12.3C13.65 12.59 13.65 13.07 13.36 13.36C13.21 13.51 13.02 13.58 12.83 13.58C12.64 13.58 12.45 13.51 12.3 13.36L10 11.06L7.7 13.36C7.55 13.51 7.36 13.58 7.17 13.58C6.98 13.58 6.79 13.51 6.64 13.36C6.35 13.07 6.35 12.59 6.64 12.3L8.94 10L6.64 7.7C6.35 7.41 6.35 6.93 6.64 6.64C6.93 6.35 7.41 6.35 7.7 6.64L10 8.94L12.3 6.64C12.59 6.35 13.07 6.35 13.36 6.64C13.65 6.93 13.65 7.41 13.36 7.7L11.06 10L13.36 12.3Z"
                                            fill="white"></path>
                                    </svg>
                                    <span> <?php echo e(__('frontend.cancel_order')); ?></span>
                                </button>
                            </a>
                        <?php endif; ?>
                        <?php if($order->attachment): ?>
                            <a href="<?php echo e(route('account.order.file', $order->id)); ?>">
                                <button class="print" type="button">
                                    <span> <?php echo e(__('frontend.download')); ?></span>
                                </button>
                            </a>
                        <?php endif; ?>
                        <?php if(!$order->attachment): ?>
                            <button class="print" type="button" onclick="printDiv('invoice-print')">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M7 5C7 3.34 8.34 2 10 2H14C15.66 2 17 3.34 17 5C17 5.55 16.55 6 16 6H8C7.45 6 7 5.55 7 5Z"
                                        fill="white"></path>
                                    <path
                                        d="M17.75 15C17.75 15.41 17.41 15.75 17 15.75H16V19C16 20.66 14.66 22 13 22H11C9.34 22 8 20.66 8 19V15.75H7C6.59 15.75 6.25 15.41 6.25 15C6.25 14.59 6.59 14.25 7 14.25H17C17.41 14.25 17.75 14.59 17.75 15Z"
                                        fill="white"></path>
                                    <path
                                        d="M18 7H6C4 7 3 8 3 10V15C3 17 4 18 6 18H6.375C6.72018 18 7 17.7202 7 17.375C7 17.0298 6.71131 16.7604 6.38841 16.6384C5.72619 16.3882 5.25 15.7453 5.25 15C5.25 14.04 6.04 13.25 7 13.25H17C17.96 13.25 18.75 14.04 18.75 15C18.75 15.7453 18.2738 16.3882 17.6116 16.6384C17.2887 16.7604 17 17.0298 17 17.375C17 17.7202 17.2798 18 17.625 18H18C20 18 21 17 21 15V10C21 8 20 7 18 7ZM10 11.75H7C6.59 11.75 6.25 11.41 6.25 11C6.25 10.59 6.59 10.25 7 10.25H10C10.41 10.25 10.75 10.59 10.75 11C10.75 11.41 10.41 11.75 10 11.75Z"
                                        fill="white"></path>
                                </svg>
                                <span><?php echo e(__('frontend.print')); ?></span>
                            </button>
                        <?php endif; ?>
                    </div>

                </div>
            </div>
        </div>
    </section>
    <!--======= SETTINGS PART END ====-->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\OSPanel\domains\click\resources\views/frontend/account/order_details.blade.php ENDPATH**/ ?>