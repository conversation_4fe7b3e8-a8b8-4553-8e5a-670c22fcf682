<?php

namespace App\Http\Services;

use App\Http\Services\BasePaymentGateway;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ClickService implements BasePaymentGateway
{
    protected $merchantId;
    protected $serviceId;
    protected $secretKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->merchantId = setting('click_merchant_id');
        $this->serviceId = setting('click_service_id');
        $this->secretKey = setting('click_secret_key');
        $this->baseUrl = 'https://my.click.uz/services/pay';
    }

    public function configaration()
    {
        return [
            'merchant_id' => $this->merchantId,
            'service_id' => $this->serviceId,
            'base_url' => $this->baseUrl
        ];
    }

    public function payment(array $parameters)
    {
        try {
            $orderId = $parameters['order_id'] ?? uniqid('click_');
            $amount = $parameters['amount'] ?? 0;

            // Click Button метод - простое перенаправление согласно документации
            $paymentUrl = $this->baseUrl . '?' . http_build_query([
                'service_id' => $this->serviceId,
                'merchant_id' => $this->merchantId,
                'amount' => number_format($amount, 2, '.', ''),
                'transaction_param' => $orderId,
                'return_url' => route('click.success'),
            ]);

            Log::info('Click Button payment initiated', [
                'order_id' => $orderId,
                'amount' => $amount,
                'formatted_amount' => number_format($amount, 2, '.', ''),
                'merchant_id' => $this->merchantId,
                'service_id' => $this->serviceId,
                'payment_url' => $paymentUrl
            ]);

            return [
                'success' => true,
                'redirect_url' => $paymentUrl,
                'order_id' => $orderId
            ];

        } catch (\Exception $e) {
            Log::error('Click service error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Payment service error: ' . $e->getMessage()
            ];
        }
    }

    public function redirectUrl()
    {
        // Этот метод может использоваться для получения URL для редиректа
        return null;
    }

    public function verifyPayment($paymentId)
    {
        // Для Click Button верификация происходит через callback
        // Здесь можно добавить дополнительную логику если нужно
        Log::info('Click payment verification', [
            'payment_id' => $paymentId
        ]);

        return true; // Для простоты считаем платеж успешным
    }



} 