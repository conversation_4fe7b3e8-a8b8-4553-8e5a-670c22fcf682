<?php

namespace App\Http\Services;

use App\Http\Services\BasePaymentGateway;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ClickService implements BasePaymentGateway
{
    protected $merchantId;
    protected $serviceId;
    protected $secretKey;
    protected $userId;
    protected $baseUrl;

    public function __construct()
    {
        $this->merchantId = setting('click_merchant_id');
        $this->serviceId = setting('click_service_id');
        $this->secretKey = setting('click_secret_key');
        $this->userId = setting('click_user_id'); // Добавляем user_id
        $this->baseUrl = 'https://api.click.uz/v2/merchant';
    }

    public function configaration()
    {
        return [
            'merchant_id' => $this->merchantId,
            'service_id' => $this->serviceId,
            'secret_key' => $this->secretKey,
            'base_url' => $this->baseUrl
        ];   
    }

    public function payment(array $parameters)
    {
        try {
            $orderId = $parameters['order_id'] ?? uniqid('click_');
            $amount = $parameters['amount'] ?? 0;
            $phoneNumber = $parameters['phone_number'] ?? '';

            // Создаем инвойс через Merchant API
            $invoiceData = [
                'service_id' => $this->serviceId,
                'merchant_trans_id' => $orderId,
                'amount' => (float) $amount,
                'phone_number' => $phoneNumber
            ];

            // Создаем HTTP клиент с правильными заголовками
            $timestamp = time();
            $authHeader = $this->userId . ':' . sha1($timestamp . $this->secretKey) . ':' . $timestamp;

            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'Auth' => $authHeader
            ])->post($this->baseUrl . '/invoice/create', $invoiceData);

            if ($response->successful()) {
                $result = $response->json();

                if (isset($result['invoice_id'])) {
                    // Формируем URL для оплаты
                    $paymentUrl = 'https://my.click.uz/services/pay?' . http_build_query([
                        'service_id' => $this->serviceId,
                        'merchant_id' => $this->merchantId,
                        'amount' => number_format($amount, 2, '.', ''),
                        'transaction_param' => $orderId,
                        'return_url' => route('click.success'),
                    ]);

                    return [
                        'success' => true,
                        'redirect_url' => $paymentUrl,
                        'order_id' => $orderId,
                        'invoice_id' => $result['invoice_id']
                    ];
                }
            }

            Log::error('Click invoice creation failed', [
                'response' => $response->body(),
                'status' => $response->status(),
                'data' => $invoiceData
            ]);

            return [
                'success' => false,
                'message' => 'Invoice creation failed: ' . $response->body()
            ];

        } catch (\Exception $e) {
            Log::error('Click service error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Payment service error: ' . $e->getMessage()
            ];
        }
    }

    public function redirectUrl()
    {
        // Этот метод может использоваться для получения URL для редиректа
        return null;
    }

    public function verifyPayment($paymentId)
    {
        try {
            $data = [
                'merchant_id' => $this->merchantId,
                'service_id' => $this->serviceId,
                'payment_id' => $paymentId
            ];

            $signature = $this->generateSignature($data);
            $data['signature'] = $signature;

            $response = Http::post($this->baseUrl . '/payment/verify', $data);

            if ($response->successful()) {
                $result = $response->json();
                return $result['result']['status'] === 'success';
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Click verification error', [
                'message' => $e->getMessage(),
                'payment_id' => $paymentId
            ]);
            return false;
        }
    }

    public function createInvoice($parameters)
    {
        try {
            $orderId = $parameters['order_id'] ?? uniqid('click_');
            $amount = $parameters['amount'] ?? 0;
            $description = $parameters['description'] ?? 'Food delivery order';

            $data = [
                'merchant_id' => $this->merchantId,
                'service_id' => $this->serviceId,
                'amount' => $amount,
                'currency' => 'UZS',
                'description' => $description,
                'order_id' => $orderId,
                'return_url' => route('click.success'),
                'cancel_url' => route('click.cancel'),
            ];

            $signature = $this->generateSignature($data);
            $data['signature'] = $signature;

            $response = Http::post($this->baseUrl . '/invoice/create', $data);

            if ($response->successful()) {
                $result = $response->json();
                if (isset($result['result']['invoice_id'])) {
                    return [
                        'success' => true,
                        'invoice_id' => $result['result']['invoice_id'],
                        'order_id' => $orderId
                    ];
                }
            }

            return [
                'success' => false,
                'message' => 'Invoice creation failed'
            ];

        } catch (\Exception $e) {
            Log::error('Click invoice creation error', [
                'message' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Invoice service error'
            ];
        }
    }

    private function generateSignature($data)
    {
        // Создаем подпись для Click API
        $signString = '';
        foreach ($data as $key => $value) {
            if ($key !== 'signature') {
                $signString .= $key . '=' . $value . '&';
            }
        }
        $signString = rtrim($signString, '&');
        
        return hash_hmac('sha256', $signString, $this->secretKey);
    }
} 