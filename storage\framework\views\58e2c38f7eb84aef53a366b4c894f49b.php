<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- AUTHOR -->
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />

    <?php echo $__env->yieldPushContent('meta'); ?>

    <!-- WEBPAGE TITLE -->
    <title>
        <?php if(isset($site_title) && setting('site_name')): ?>
            <?php echo e(setting('site_name') . ' : ' . $site_title); ?>

        <?php elseif(setting('site_name')): ?>
            <?php echo e(setting('site_name')); ?>

        <?php elseif($site_title): ?>
            <?php echo e($site_title); ?>

        <?php else: ?>
            <?php echo e(''); ?>

        <?php endif; ?>
    </title>

    <!-- FAVICON -->
    <link href="<?php echo e(asset('images/' . setting('fav_icon'))); ?>" rel="shortcut icon" type="image/x-icon">
    <!-- LIBRARY -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/lib/swiper/swiper-bundle.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('frontend/lib/bootstrap/bootstrap.min.css')); ?>">
    <!-- FONTS -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/fonts/lineicons/lineicons.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('frontend/fonts/fontawesome/fontawesome.min.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('frontend/fonts/opensauce/opensauce.min.css')); ?>">
    <!-- iziToast -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/lib/izitoast/dist/css/iziToast.min.css')); ?>">
    <!-- CUSTOM -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/css/expanded/style.css')); ?>">
    <!-- My Custom Css -->
    <link rel="stylesheet" href="<?php echo e(asset('frontend/css/expanded/custom.css')); ?>">

    <?php echo $__env->yieldPushContent('style'); ?>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>

</head>
<?php /**PATH D:\OSPanel\domains\click\resources\views/frontend/partials/_head.blade.php ENDPATH**/ ?>